/**
 * 题目生成器测试
 */
import QuestionGenerator from './QuestionGenerator';

describe('QuestionGenerator', () => {
  let generator;

  beforeEach(() => {
    generator = new QuestionGenerator();
  });

  test('应该能生成1级题目（两位数加减法）', () => {
    const question = generator.generateQuestion(1);
    
    expect(question).toHaveProperty('question');
    expect(question).toHaveProperty('answer');
    expect(typeof question.answer).toBe('number');
    
    // 验证问题格式
    expect(question.question).toMatch(/^\d+ \+ \d+ = \?$/);
  });

  test('应该能生成2级题目（三位数加减法）', () => {
    const question = generator.generateQuestion(2);
    
    expect(question).toHaveProperty('question');
    expect(question).toHaveProperty('answer');
    expect(typeof question.answer).toBe('number');
  });

  test('应该能生成3级题目（乘法表应用）', () => {
    const question = generator.generateQuestion(3);
    
    expect(question).toHaveProperty('question');
    expect(question).toHaveProperty('answer');
    expect(typeof question.answer).toBe('number');
    
    // 验证问题格式
    expect(question.question).toMatch(/^\d+ × \d+ = \?$/);
  });

  test('应该能生成4级题目（乘除法）', () => {
    const question = generator.generateQuestion(4);
    
    expect(question).toHaveProperty('question');
    expect(question).toHaveProperty('answer');
    expect(typeof question.answer).toBe('number');
  });

  test('应该能生成5级题目（复杂应用题）', () => {
    const question = generator.generateQuestion(5);
    
    expect(question).toHaveProperty('question');
    expect(question).toHaveProperty('answer');
    expect(typeof question.answer).toBe('number');
  });

  test('应该能正确验证答案', () => {
    const question = generator.generateQuestion(1);
    const correctAnswer = question.answer;
    const wrongAnswer = correctAnswer + 1;
    
    expect(generator.validateAnswer({ id: 'test', answer: correctAnswer }, correctAnswer)).toBe(true);
    expect(generator.validateAnswer({ id: 'test', answer: correctAnswer }, wrongAnswer)).toBe(false);
  });

  test('应该能正确处理缓存', () => {
    const cacheStatus = generator.getCacheStatus();
    
    expect(cacheStatus).toHaveProperty('size');
    expect(cacheStatus).toHaveProperty('maxSize');
    expect(cacheStatus).toHaveProperty('keys');
  });
});