/**
 * 数学题生成器
 * 根据难度级别动态生成符合课程标准的数学问题
 */
class QuestionGenerator {
  constructor() {
    this.difficultyLevels = [
      { level: 1, type: 'addition_subtraction', min: 10, max: 50 },
      { level: 2, type: 'addition_subtraction', min: 100, max: 300 },
      { level: 3, type: 'multiplication', min: 1, max: 5 },
      { level: 4, type: 'mixed_operations', min: 6, max: 9 },
      { level: 5, type: 'complex_word_problems', min: 10, max: 20 }
    ];
    
    this.cache = new Map();
    this.cacheSize = 100;
  }

  /**
   * 根据级别生成数学题
   * @param {number} level - 题目难度级别 (1-5)
   * @returns {Object} 包含问题和答案的对象
   */
  generateQuestion(level) {
    // 检查缓存中是否有已生成的题目
    const cacheKey = `level_${level}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    let question, answer;
    
    switch (level) {
      case 1: // 两位数加减法（无进位）
        question = this.generateAdditionSubtraction(10, 50);
        break;
      case 2: // 三位数加减法（进位）
        question = this.generateAdditionSubtraction(100, 300);
        break;
      case 3: // 乘法表应用（1-5）
        question = this.generateMultiplication(1, 5);
        break;
      case 4: // 乘法表应用（6-9）+简单除法
        question = this.generateMixedOperations(6, 9);
        break;
      case 5: // 混合运算+简单应用题
        question = this.generateComplexWordProblems();
        break;
      default:
        throw new Error(`不支持的难度级别: ${level}`);
    }

    answer = question.answer;

    // 缓存生成的问题
    if (this.cache.size >= this.cacheSize) {
      // 移除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(cacheKey, { question: question.question, answer });
    
    return { question: question.question, answer };
  }

  /**
   * 生成加减法题目
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {Object}
   */
  generateAdditionSubtraction(min, max) {
    const a = Math.floor(Math.random() * (max - min + 1)) + min;
    const b = Math.floor(Math.random() * (max - min + 1)) + min;
    
    // 随机选择加法或减法
    if (Math.random() > 0.5) {
      return {
        question: `${a} + ${b} = ?`,
        answer: a + b
      };
    } else {
      const larger = Math.max(a, b);
      const smaller = Math.min(a, b);
      return {
        question: `${larger} - ${smaller} = ?`,
        answer: larger - smaller
      };
    }
  }

  /**
   * 生成乘法题目
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {Object}
   */
  generateMultiplication(min, max) {
    const a = Math.floor(Math.random() * (max - min + 1)) + min;
    const b = Math.floor(Math.random() * (max - min + 1)) + min;
    
    return {
      question: `${a} × ${b} = ?`,
      answer: a * b
    };
  }

  /**
   * 生成混合运算题目
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {Object}
   */
  generateMixedOperations(min, max) {
    const a = Math.floor(Math.random() * (max - min + 1)) + min;
    const b = Math.floor(Math.random() * (max - min + 1)) + min;
    
    // 随机选择乘法或除法
    if (Math.random() > 0.5) {
      return {
        question: `${a} × ${b} = ?`,
        answer: a * b
      };
    } else {
      const larger = Math.max(a, b);
      const smaller = Math.min(a, b);
      
      // 确保能整除
      if (larger % smaller === 0) {
        return {
          question: `${larger} ÷ ${smaller} = ?`,
          answer: larger / smaller
        };
      } else {
        // 如果不能整除，生成一个简单的除法题
        const dividend = smaller * Math.floor(Math.random() * (max - min + 1)) + min;
        return {
          question: `${dividend} ÷ ${smaller} = ?`,
          answer: dividend / smaller
        };
      }
    }
  }

  /**
   * 生成复杂应用题
   * @returns {Object}
   */
  generateComplexWordProblems() {
    const problems = [
      // 混合运算
      () => {
        const a = Math.floor(Math.random() * 10) + 5;
        const b = Math.floor(Math.random() * 5) + 2;
        return {
          question: `小明有${a}个苹果，他吃掉了${b}个，请问还剩多少？`,
          answer: a - b
        };
      },
      
      // 简单乘法应用题
      () => {
        const items = Math.floor(Math.random() * 5) + 2;
        const price = Math.floor(Math.random() * 10) + 5;
        return {
          question: `一个苹果${price}元，小红买了${items}个，请问一共花了多少钱？`,
          answer: items * price
        };
      },
      
      // 简单除法应用题
      () => {
        const total = Math.floor(Math.random() * 20) + 10;
        const groups = Math.floor(Math.random() * 4) + 2;
        return {
          question: `小明有${total}个糖果，要平均分给${groups}个朋友，请问每人能分到几个？`,
          answer: total / groups
        };
      }
    ];
    
    const problem = problems[Math.floor(Math.random() * problems.length)];
    return problem();
  }

  /**
   * 验证答案是否正确
   * @param {number} questionId - 问题ID（在实际应用中使用）
   * @param {string} userAnswer - 用户的答案
   * @param {number} correctAnswer - 正确答案
   * @returns {boolean}
   */
  validateAnswer(questionId, userAnswer, correctAnswer) {
    // 这里可以添加更复杂的验证逻辑，比如处理小数、分数等
    return Number(userAnswer) === correctAnswer;
  }

  /**
   * 获取题目缓存状态
   */
  getCacheStatus() {
    return {
      size: this.cache.size,
      maxSize: this.cacheSize,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
  }
}

export default QuestionGenerator;