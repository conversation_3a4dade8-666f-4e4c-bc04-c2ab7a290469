/* 游戏界面CSS样式文件 */
/* 基础重置和变量定义 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 主题颜色方案 */
  --primary-color: #4361ee;
  --secondary-color: #3a0ca0;
  --accent-color: #f72585;
  --light-color: #ffffff;
  --dark-color: #1d3557;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  
  /* 字体 */
  --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-small: 12px;
  --font-size-medium: 16px;
  --font-size-large: 20px;
  --font-size-xlarge: 24px;
  
  /* 响应式断点 */
  --breakpoint-mobile: 320px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
}

/* 基础样式 */
body {
  font-family: var(--font-primary);
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
  transition: background-color 0.3s ease;
}

/* 响应式设计 - 移动设备 */
@media (max-width: 480px) {
  body {
    font-size: var(--font-size-small);
  }
  
  .game-container {
    padding: 10px;
    margin: 5px;
  }
  
  .game-header {
    flex-direction: column;
    gap: 10px;
  }
}

/* 响应式设计 - 平板设备 */
@media (min-width: 481px) and (max-width: 768px) {
  body {
    font-size: var(--font-size-medium);
  }
  
  .game-container {
    padding: 20px;
    margin: 15px;
  }
}

/* 响应式设计 - 桌面设备 */
@media (min-width: 769px) {
  body {
    font-size: var(--font-size-medium);
  }
  
  .game-container {
    padding: 30px;
    margin: 20px auto;
    max-width: 1200px;
  }
}

/* 游戏区域布局样式 */
.game-container {
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin: 20px auto;
  max-width: 1200px;
  transition: all 0.3s ease;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

/* 游戏标题样式 */
.game-title {
  font-size: var(--font-size-xlarge);
  color: var(--primary-color);
  font-weight: bold;
  text-align: center;
}

/* 按钮样式 - 基础按钮 */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-medium);
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* 主要按钮 */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #3a0ca0;
  color: white;
}

/* 次要按钮 */
.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #2d1b6e;
  color: white;
}

/* 成功按钮 */
.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #388E3C;
  color: white;
}

/* 警告按钮 */
.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: #e68a00;
  color: white;
}

/* 错误按钮 */
.btn-error {
  background-color: var(--error-color);
  color: white;
}

.btn-error:hover {
  background-color: #d32f2f;
  color: white;
}

/* 游戏区域布局样式 */
.game-content {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

/* 主游戏区域 */
.main-game-area {
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 25px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 游戏问题区域 */
.question-area {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 游戏选项区域 */
.options-container {
  display: grid;
  gap: 15px;
  margin-top: 20px;
}

.option-button {
  background-color: #f0f4ff;
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  padding: 15px 20px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--font-size-medium);
}

.option-button:hover {
  background-color: #e6f0ff;
  transform: translateX(5px);
}

/* 游戏状态显示 */
.game-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8fafc;
}

.score-display {
  font-size: var(--font-size-large);
  color: var(--primary-color);
  font-weight: bold;
}

/* 响应式布局 - 多列显示 */
@media (min-width: 769px) {
  .game-content {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
  }
  
  .main-game-area {
    grid-column: span 2;
  }
}

/* 响应式布局 - 移动端 */
@media (max-width: 768px) {
  .game-container {
    padding: 15px;
    margin: 10px;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* 游戏反馈样式 */
.feedback {
  padding: 15px;
  border-radius: 8px;
  margin-top: 10px;
  text-align: center;
  font-weight: bold;
}

.feedback.success {
  background-color: #e8f5e9;
  color: var(--success-color);
}

.feedback.error {
  background-color: #ffebee;
  color: var(--error-color);
}

/* 游戏进度条 */
.progress-bar {
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 20px;
  background-color: #e9ecef;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

/* 响应式图片 */
.game-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 输入框样式 */
input[type="text"],
input[type="number"] {
  width: 100%;
  padding: 12px 15px;
  border-radius: 8px;
  border: 2px solid #ced4da;
  font-size: var(--font-size-medium);
  transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="number"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: var(--dark-color);
}

/* 响应式表格 */
.table-responsive {
  overflow-x: auto;
  max-width: 100%;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

th,
td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

/* 响应式卡片 */
.card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 15px;
}

@media (min-width: 769px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* 响应式文本 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 响应式间距 */
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

/* 响应式显示 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: block;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}