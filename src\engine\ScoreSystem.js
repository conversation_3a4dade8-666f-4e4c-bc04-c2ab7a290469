/**
 * 分数计算系统
 * 负责处理游戏中的分数计算、奖励等逻辑
 */

class ScoreSystem {
  constructor() {
    this.basePoints = 10;
    this.multiplier = 1.0;
    this.comboCount = 0;
    this.maxCombo = 0;
    this.totalScore = 0;
  }

  /**
   * 计算分数（基础）
   */
  calculateBaseScore(isCorrect) {
    if (isCorrect) {
      const points = Math.round(this.basePoints * this.multiplier);
      this.totalScore += points;
      return points;
    }
    return 0;
  }

  /**
   * 处理连击
   */
  handleCombo(isCorrect) {
    if (isCorrect) {
      this.comboCount++;
      // 每5个连击增加倍数
      if (this.comboCount % 5 === 0) {
        this.multiplier = Math.min(this.multiplier + 0.2, 3.0);
      }
      return true;
    } else {
      this.comboCount = 0;
      this.multiplier = 1.0;
      return false;
    }
  }

  /**
   * 获取当前分数
   */
  getCurrentScore() {
    return {
      total: this.totalScore,
      combo: this.comboCount,
      multiplier: this.multiplier
    };
  }

  /**
   * 重置系统
   */
  reset() {
    this.basePoints = 10;
    this.multiplier = 1.0;
    this.comboCount = 0;
    this.totalScore = 0;
  }

  /**
   * 获取最高连击数
   */
  getMaxCombo() {
    return Math.max(this.maxCombo, this.comboCount);
  }
}

export default ScoreSystem;