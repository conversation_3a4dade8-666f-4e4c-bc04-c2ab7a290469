/**
 * 核心游戏引擎
 * 负责游戏状态管理、分数计算、级别递增等核心功能
 */
import { default as ScoreSystem } from './ScoreSystem';
import { default as QuestionGenerator } from './QuestionGenerator';

class GameEngine {
  constructor() {
    this.gameState = 'idle'; // idle, playing, finished
    this.score = 0;
    this.level = 1;
    this.currentQuestion = 0;
    this.questions = [];
    this.starsEarned = 0;
    this.questionGenerator = new QuestionGenerator();
    
    // 游戏状态监听器
    this.listeners = [];
  }

  /**
   * 开始游戏
   */
  startGame() {
    if (this.gameState === 'idle') {
      this.gameState = 'playing';
      this.score = 0;
      this.level = 1;
      this.currentQuestion = 0;
      this.questions = [];
      
      // 触发开始事件
      this.emit('gameStarted', { level: this.level });
    }
  }

  /**
   * 结束游戏
   */
  endGame() {
    if (this.gameState === 'playing') {
      this.gameState = 'finished';
      this.emit('gameFinished', { 
        score: this.score, 
        level: this.level,
        stars: this.starsEarned
      });
    }
  }

  /**
   * 处理用户回答
   */
  handleAnswer(isCorrect) {
    if (this.gameState !== 'playing') return;
    
    // 更新分数
    const points = isCorrect ? 10 : 0;
    this.score += points;
    
    // 更新问题计数
    this.currentQuestion++;
    
    // 触发回答处理事件
    this.emit('answerProcessed', { 
      correct: isCorrect, 
      score: this.score,
      questionNumber: this.currentQuestion
    });
    
    if (isCorrect) {
      this.emit('questionCorrect');
    } else {
      this.emit('questionIncorrect');
    }
  }

  /**
   * 升级级别
   */
  upgradeLevel() {
    if (this.gameState !== 'playing') return;
    
    this.level++;
    this.starsEarned = Math.min(this.starsEarned + 1, 5); // 最多5颗星
    
    // 触发升级事件
    this.emit('levelUp', { 
      newLevel: this.level,
      stars: this.starsEarned
    });
    
    return this.level;
  }

  /**
   * 获取当前游戏状态
   */
  getGameState() {
    return {
      state: this.gameState,
      score: this.score,
      level: this.level,
      currentQuestion: this.currentQuestion,
      stars: this.starsEarned
    };
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }

  /**
   * 获取游戏状态字符串
   */
  getGameStateString() {
    return this.gameState;
  }

  /**
   * 根据级别生成问题
   * @param {number} level - 题目难度级别 (1-5)
   * @returns {Object} 包含问题和答案的对象
   */
  generateQuestion(level) {
    if (level < 1 || level > 5) {
      throw new Error(`无效的题目级别: ${level}`);
    }
    
    const questionData = this.questionGenerator.generateQuestion(level);
    return {
      id: Date.now() + Math.random(),
      question: questionData.question,
      answer: questionData.answer,
      level: level
    };
  }

  /**
   * 验证答案是否正确
   * @param {Object} question - 问题对象
   * @param {string} userAnswer - 用户的答案
   * @returns {boolean}
   */
  validateAnswer(question, userAnswer) {
    return this.questionGenerator.validateAnswer(question.id, userAnswer, question.answer);
  }
}

export default GameEngine;