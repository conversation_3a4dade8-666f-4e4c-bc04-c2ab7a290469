/**
 * 游戏主交互控制器
 * 负责处理游戏初始化、用户输入、分数更新和级别切换等核心功能
 */

// 导入游戏引擎组件
import GameEngine from './engine/GameEngine.js';
import InteractionHandler from './engine/InteractionHandler.js';

class GameController {
    constructor() {
        // 初始化游戏引擎
        this.gameEngine = new GameEngine();
        
        // 初始化交互处理模块
        this.interactionHandler = new InteractionHandler();
        
        // 游戏状态管理
        this.isGameActive = false;
        this.currentLevel = 1;
        
        // DOM元素引用
        this.scoreDisplay = null;
        this.levelDisplay = null;
        this.gameStatus = null;
        
        // 初始化游戏界面
        this.initGameInterface();
    }
    
    /**
     * 游戏初始化和启动功能
     */
    init() {
        console.log('游戏控制器已初始化');
        
        // 设置DOM元素引用
        this.scoreDisplay = document.getElementById('score-display') || 
                         document.querySelector('.score-display') || 
                         document.createElement('div');
        this.levelDisplay = document.getElementById('level-display') || 
                             document.querySelector('.level-display') || 
                             document.createElement('div');
        this.gameStatus = document.getElementById('game-status') || 
                        document.querySelector('.game-status') || 
                        document.createElement('div');
        
        // 初始化交互监听器
        this.interactionHandler.init();
        
        // 设置键盘事件监听器
        this.setupKeyboardListeners();
        
        // 游戏状态监听器
        this.setupGameEventListeners();
        
        console.log('游戏初始化完成');
    }
    
    /**
     * 开始游戏
     */
console.log('开始游戏按钮被点击，调用 startGame() 方法');
    startGame() {
        if (!this.isGameActive) {
            this.isGameActive = true;
            
            // 重置游戏引擎
            this.gameEngine.startGame();
            
            // 更新界面显示
            this.updateDisplay();
            
            console.log('游戏已开始');
        }
    }
    
    /**
     * 结束游戏
     */
    endGame() {
        if (this.isGameActive) {
            this.isGameActive = false;
            this.gameEngine.endGame();
            console.log('游戏已结束');
        }
    }
    
    /**
     * 设置键盘事件监听器
     */
    setupKeyboardListeners() {
        // 监听键盘按下事件
        document.addEventListener('keydown', (event) => {
            if (!this.isGameActive) return;
            
            const key = event.key.toLowerCase();
            
            // 处理用户输入
            this.handleUserInput(key);
            
            // 触发交互处理
            this.interactionHandler.handleInput('keyPress', { 
                key: event.key, 
                code: event.code,
                timestamp: Date.now()
            });
        });
    }
    
    /**
     * 处理用户输入
     */
    handleUserInput(key) {
        // 这里可以添加特定的键盘控制逻辑
        switch (key) {
            case ' ': // 空格键 - 提交答案
                console.log('用户按下了空格键');
                break;
            case 'enter': // 回车键 - 提交答案
                console.log('用户按下了回车键');
                break;
            case 'arrowleft':
                console.log('用户按下了左箭头键');
                break;
            case 'arrowright':
                console.log('用户按下了右箭头键');
                break;
            case 'arrowup':
                console.log('用户按下了上箭头键');
                break;
            case 'arrowdown':
                console.log('用户按下了下箭头键');
                break;
        }
    }
    
    /**
     * 设置游戏事件监听器
console.log('设置游戏开始事件监听器');
     */
    setupGameEventListeners() {
        // 监听游戏开始事件
        this.gameEngine.on('gameStarted', (data) => {
            console.log('游戏已启动:', data);
            this.updateDisplay();
        });
        
        // 监听回答处理事件
        this.gameEngine.on('answerProcessed', (data) => {
            console.log('答案已处理:', data);
            this.updateScore(data.score);
            this.updateLevel(this.gameEngine.getGameState().level);
        });
        
        // 监听级别升级事件
        this.gameEngine.on('levelUp', (data) => {
            console.log('级别已提升:', data);
            this.handleLevelChange();
        });
        
        // 监听游戏结束事件
        this.gameEngine.on('gameFinished', (data) => {
            console.log('游戏已结束:', data);
            this.updateDisplay();
        });
    }
    
    /**
     * 实时分数更新显示
     */
    updateScore(score) {
        if (this.scoreDisplay) {
            // 如果有特定的分数显示元素，就更新它
            this.scoreDisplay.textContent = `分数: ${score}`;
            this.scoreDisplay.style.color = score > 100 ? '#4CAF50' : 
                                             score > 50 ? '#FF9800' : '#F44336';
        }
    }
    
    /**
     * 实时级别更新显示
     */
    updateLevel(level) {
        if (this.levelDisplay) {
            this.levelDisplay.textContent = `级别: ${level}`;
            this.levelDisplay.style.color = level > 3 ? '#2196F3' : 
                                             level > 1 ? '#FFC107' : '#FF5722';
        }
    }
    
    /**
     * 更新游戏状态显示
     */
    updateDisplay() {
        const gameState = this.gameEngine.getGameState();
        
        // 更新分数和级别显示
        if (this.scoreDisplay) {
            this.scoreDisplay.textContent = `分数: ${gameState.score}`;
        }
        
        if (this.levelDisplay) {
            this.levelDisplay.textContent = `级别: ${gameState.level}`;
        }
        
        // 根据游戏状态更新界面
        if (this.gameStatus) {
            const statusText = gameState.state === 'playing' ? 
                               '游戏中' : 
                               gameState.state === 'finished' ? 
                               '游戏结束' : 
                               '等待开始';
            this.gameStatus.textContent = `状态: ${statusText}`;
        }
    }
    
    /**
     * 处理级别切换逻辑
     */
    handleLevelChange() {
        const currentLevel = this.gameEngine.getGameState().level;
        
        // 根据新级别更新游戏难度或内容
        console.log(`已切换到级别 ${currentLevel}`);
        
        // 可以在这里添加特定级别的处理逻辑
        
        // 更新显示
        this.updateDisplay();
    }
    
    /**
     * 处理用户答案提交
     */
    submitAnswer(answer) {
        if (!this.isGameActive) return;
        
        // 验证答案并处理
        const isCorrect = this.gameEngine.validateAnswer(
            this.gameEngine.getGameState().currentQuestion, 
            answer
        );
        
        // 处理用户回答
        this.gameEngine.handleAnswer(isCorrect);
    }
    
    /**
     * 初始化游戏界面（如果需要）
     */
    initGameInterface() {
        // 这里可以添加初始化游戏界面的逻辑
        console.log('正在初始化游戏界面...');
    }
}

// 导出控制器实例供其他模块使用
export default new GameController();