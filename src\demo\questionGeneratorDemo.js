/**
 * 题目生成系统演示
 * 展示5个级别数学题的生成、难度递增算法和答案验证机制
 */

import QuestionGenerator from '../../engine/QuestionGenerator';
import GameEngine from '../../engine/GameEngine';

console.log('=== 数学题目生成器演示 ===\n');

// 创建题目生成器实例
const questionGenerator = new QuestionGenerator();
const gameEngine = new GameEngine();

console.log('1. 一级题目（两位数加减法）:');
try {
  const level1Question = questionGenerator.generateQuestion(1);
  console.log(`   题目: ${level1Question.question}`);
  console.log(`   答案: ${level1Question.answer}`);
} catch (error) {
  console.error('生成一级题目失败:', error.message);
}

console.log('\n2. 二级题目（三位数加减法）:');
try {
  const level2Question = questionGenerator.generateQuestion(2);
  console.log(`   题目: ${level2Question.question}`);
  console.log(`   答案: ${level2Question.answer}`);
} catch (error) {
  console.error('生成二级题目失败:', error.message);
}

console.log('\n3. 三级题目（乘法表应用）:');
try {
  const level3Question = questionGenerator.generateQuestion(3);
  console.log(`   题目: ${level3Question.question}`);
  console.log(`   答案: ${level3Question.answer}`);
} catch (error) {
  console.error('生成三级题目失败:', error.message);
}

console.log('\n4. 四级题目（乘除法）:');
try {
  const level4Question = questionGenerator.generateQuestion(4);
  console.log(`   题目: ${level4Question.question}`);
  console.log(`   答案: ${level4Question.answer}`);
} catch (error) {
  console.error('生成四级题目失败:', error.message);
}

console.log('\n5. 五级题目（复杂应用题）:');
try {
  const level5Question = questionGenerator.generateQuestion(5);
  console.log(`   题目: ${level5Question.question}`);
  console.log(`   答案: ${level5Question.answer}`);
} catch (error) {
  console.error('生成五级题目失败:', error.message);
}

console.log('\n6. 答案验证机制演示:');
try {
  const question = questionGenerator.generateQuestion(1);
  const correctAnswer = question.answer;
  const wrongAnswer = correctAnswer + 5;
  
  console.log(`   题目: ${question.question}`);
  console.log(`   正确答案: ${correctAnswer}`);
  
  // 测试正确答案
  if (questionGenerator.validateAnswer({ id: 'test', answer: correctAnswer }, correctAnswer)) {
    console.log('✅ 答案验证成功 - 正确答案');
  } else {
    console.log('❌ 答案验证失败 - 正确答案');
  }
  
  // 测试错误答案
  if (questionGenerator.validateAnswer({ id: 'test', answer: correctAnswer }, wrongAnswer)) {
    console.log('❌ 答案验证失败 - 错误答案');
  } else {
    console.log('✅ 答案验证成功 - 错误答案');
  }
  
} catch (error) {
  console.error('答案验证失败:', error.message);
}

console.log('\n7. 题目缓存优化:');
try {
  // 生成多个题目来测试缓存
  const questions = [];
  for (let i = 0; i < 10; i++) {
    const level = Math.floor(Math.random() * 5) + 1;
    const question = questionGenerator.generateQuestion(level);
    questions.push(question);
  }
  
  console.log(`   已生成 ${questions.length} 道题目`);
  console.log(`   缓存状态: ${questionGenerator.getCacheStatus().size} 项缓存`);
  
  // 清除缓存
  questionGenerator.clearCache();
  console.log('   缓存已清除');
  
} catch (error) {
  console.error('缓存测试失败:', error.message);
}

console.log('\n=== 演示完成 ===');

// 难度递增算法演示
console.log('\n8. 难度递增算法:');
try {
  // 模拟游戏过程中的难度变化
  const difficultyLevels = [1, 2, 3, 4, 5];
  
  console.log('   题目级别: ' + difficultyLevels.join(', '));
  
  for (let i = 0; i < difficultyLevels.length; i++) {
    const level = difficultyLevels[i];
    const question = questionGenerator.generateQuestion(level);
    
    if (i === 0) {
      console.log(`   级别${level}: ${question.question} = ?`);
    } else {
      console.log(`   级别${level}: ${question.question} = ?`);
    }
  }
  
  console.log('   难度递增算法已演示');
  
} catch (error) {
  console.error('难度递增算法演示失败:', error.message);
}

console.log('\n=== 演示结束 ===');