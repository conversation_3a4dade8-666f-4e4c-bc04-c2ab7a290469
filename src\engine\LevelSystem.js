/**
 * 级别递增机制
 * 负责处理游戏中的关卡推进、难度提升等逻辑
 */

class LevelSystem {
  constructor() {
    this.currentLevel = 1;
    this.starsEarned = 0;
    this.questionsAnswered = 0;
    this.levelProgress = 0;
    this.requiredQuestions = 5; // 每个关卡需要答对的题目数
  }

  /**
   * 处理关卡完成
   */
  completeLevel() {
    if (this.currentLevel < 5) { // 假设最多5个关卡
      this.currentLevel++;
      this.starsEarned = Math.min(this.starsEarned + 1, 3); // 最多3颗星
      return true;
    }
    return false;
  }

  /**
   * 检查是否完成当前关卡
   */
  checkLevelCompletion(isCorrect) {
    if (isCorrect) {
      this.questionsAnswered++;
      
      // 如果答对了题目，检查是否完成关卡
      if (this.questionsAnswered >= this.requiredQuestions) {
        return true;
      }
    } else {
      // 错误时重置计数器
      this.questionsAnswered = 0;
    }
    
    return false;
  }

  /**
   * 获取当前级别信息
   */
  getCurrentLevelInfo() {
    return {
      level: this.currentLevel,
      stars: this.starsEarned,
      questionsAnswered: this.questionsAnswered,
      requiredQuestions: this.requiredQuestions,
      progress: (this.questionsAnswered / this.requiredQuestions) * 100
    };
  }

  /**
   * 升级级别
   */
  upgradeLevel() {
    if (this.currentLevel < 5) {
      this.currentLevel++;
      return true;
    }
    return false;
  }

  /**
   * 重置系统
   */
  reset() {
    this.currentLevel = 1;
    this.starsEarned = 0;
    this.questionsAnswered = 0;
    this.levelProgress = 0;
  }

  /**
   * 获取关卡难度等级
   */
  getDifficultyLevel() {
    if (this.currentLevel <= 2) return 'easy';
    if (this.currentLevel <= 4) return 'medium';
    return 'hard';
  }
}

export default LevelSystem;