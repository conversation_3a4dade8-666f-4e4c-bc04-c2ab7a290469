# 数学冒险岛 游戏设计方案

## 目标用户
- 香港小学三年级学生（8-9岁）
- 双语界面（繁体中文为主，英文术语）

## 游戏机制
```mermaid
graph TD
    A[开始游戏] --> B{选择关卡}
    B --> C1[关卡1]
    B --> C2[关卡2]
    B --> C3[关卡3]
    B --> C4[关卡4]
    B --> C5[关卡5]
    
    C1 --> D[答5道题]
    D --> E{正确率}
    E -->|≥80%| F[获得3星]
    E -->|60-79%| G[获得2星]
    E -->|<60%| H[获得1星]
    F --> I[解锁下一关]
```

## 功能模块
1. **用户系统**
   - 本地存档（无需登录）
   - 进度追踪（关卡/星星数）
2. **题目生成器**
   - 动态生成符合课程标准的题目
   - 错误答案智能干扰项生成
3. **反馈系统**
   - 即时正误动画
   - 错题解析（文字+语音）
4. **奖励系统**
   - 关卡星级评价
   - 成就徽章收集

## 技术栈
| 模块       | 技术选择          |
|------------|------------------|
| 前端框架   | React 18         |
| 3D渲染     | Three.js         |
| UI组件库   | Material UI      |
| 后端       | Node.js + Express|
| 数据库     | SQLite           |
| 部署       | Vercel + Heroku  |

## 5级题目规范

| 级别 | 题型                      | 示例                     | 时间限制 |
|------|--------------------------|--------------------------|----------|
| 1    | 两位数加减法（无进位）    | 23+15=?, 48-12=?         | 30秒/题  |
| 2    | 三位数加减法（进位）      | 156+287=?, 420-138=?     | 45秒/题  |
| 3    | 乘法表应用（1-5）         | 4×5=?, 3×2=?             | 25秒/题  |
| 4    | 乘法表应用（6-9）+简单除法 | 7×8=?, 36÷6=?            | 35秒/题  |
| 5    | 混合运算+简单应用题       | (15-3)×2=?, "小明有20元，买书花了一半，还剩？" | 60秒/题  |

## 题目生成算法（伪代码）
```python
def generate_question(level):
    if level == 1:
        a = random.randint(10, 50)
        b = random.randint(10, 50)
        if random.choice([True, False]):
            return f"{a} + {b} = ?", a+b
        else:
            return f"{max(a,b)} - {min(a,b)} = ?", max(a,b)-min(a,b)
    
    # 其他级别生成逻辑类似...
```

## 用户进度系统
```mermaid
classDiagram
    class UserProgress {
        +String userId
        +Map<level, stars>
        +Map<level, bestTime>
        +Date lastPlayed
        +saveLocal()
        +loadLocal()
    }
```
- 使用浏览器localStorage存储进度
- 数据结构：
  ```json
  {
    "L1": { "stars": 3, "bestTime": 75 },
    "L2": { "stars": 2, "bestTime": 120 }
  }
  ```

## 响应式UI框架
1. **组件结构**
   - GameContainer (路由管理)
   - LevelSelector (关卡选择)
   - QuestionPanel (答题界面)
   - FeedbackOverlay (即时反馈)
2. **断点设计**
   - 移动端：竖屏答题界面
   - 平板：横屏双栏布局
   - 桌面：三栏布局（进度+答题+提示）

## 开发阶段
1. 核心游戏引擎（2周）
2. 题目生成系统（1周）
3. UI/UX设计实现（2周）
4. 测试与优化（1周）