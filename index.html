<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            padding: 30px;
            text-align: center;
        }

        .game-title {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .game-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .start-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
        }

        .start-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .start-button:active {
            transform: translateY(1px);
        }

        .game-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .game-info {
            color: #4a5568;
            font-size: 1rem;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .game-container {
                padding: 20px;
            }
            
            .game-title {
                font-size: 2rem;
            }
            
            .game-description {
                font-size: 1rem;
            }
            
            .start-button {
                padding: 12px 30px;
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .game-container {
                padding: 15px;
            }
            
            .game-title {
                font-size: 1.7rem;
            }
            
            .start-button {
                padding: 10px 25px;
                font-size: 1rem;
            }
        }

        /* 游戏区域样式 */
        .game-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .game-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .game-cell {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px 10px;
            font-size: 1.2rem;
            color: #333;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .game-cell:hover {
            background: #d8e2f5;
            cursor: pointer;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">数学游戏</h1>
        <p class="game-description">
            欢迎来到数学世界！在这里你可以通过有趣的挑战来提高你的数学技能。
            从基础的加减法开始，逐步提升到更复杂的运算。
        </p>
        
        <button class="start-button" onclick="startGame()">
            开始游戏
        </button>
        
        <div class="game-area">
            <h3>游戏区域</h3>
            <p class="game-info">这是您开始挑战的区域。在这里您可以：</p>
            
            <div class="game-grid">
                <div class="game-cell"><i class="fas fa-calculator"></i><br>加法</div>
                <div class="game-cell"><i class="fas fa-plus"></i><br>减法</div>
                <div class="game-cell"><i class="fas fa-times"></i><br>乘法</div>
                <div class="game-cell"><i class="fas fa-divide"></i><br>除法</div>
                <div class="game-cell"><i class="fas fa-square-root-alt"></i><br>开方</div>
                <div class="game-cell"><i class="fas fa-percent"></i><br>百分比</div>
            </div>
        </div>
    </div>

    <script>
console.log('startGame called, navigating to game screen');
        function startGame() {
// 跳转到游戏主界面的逻辑
window.location.href = 'src/game.html'; // 假设游戏主界面是 'src/game.html'
            alert('游戏开始！您将进入挑战模式。');
            // 这里可以添加跳转到游戏主界面的逻辑
        }
        
        // 响应式调整
        window.addEventListener('resize', function() {
            const container = document.querySelector('.game-container');
            if (window.innerWidth < 480) {
                container.style.padding = '15px';
            } else {
                container.style.padding = '30px';
            }
        });
    </script>
</body>
</html>